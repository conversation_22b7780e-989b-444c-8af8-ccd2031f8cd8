# 🧹 System Cleanup & Refactoring Plan

## Executive Summary

This plan addresses all architectural issues through **4 incremental phases** that can be deployed independently without breaking existing functionality. Each phase includes migration scripts, validation procedures, and rollback options.

**Timeline**: 2-3 weeks | **Risk Level**: Low (with proper testing)

---

## 🎯 Migration Strategy

### Core Principles
1. **Zero Downtime**: Maintain dual systems during transitions
2. **Incremental**: Each phase is independently deployable
3. **Reversible**: Every change has a rollback procedure
4. **Validated**: Comprehensive testing at each step

### Phase Overview
- **Phase 1**: Code Organization (1-2 days) - *Safe*
- **Phase 2**: Data Model Cleanup (3-4 days) - *Medium Risk*
- **Phase 3**: Business Logic Consolidation (4-5 days) - *Higher Risk*
- **Phase 4**: Advanced Optimizations (2-3 days) - *Safe*

---

## 📋 Phase 1: Code Organization & Standardization
*Risk Level: **Low** | Duration: 1-2 days*

### 1.1 Standardize Database Access Pattern

**Goal**: Single, consistent way to access database

**Current Issues**:
```typescript
// Three different patterns exist:
import { db } from '@/lib/db'                    // Legacy
import { getEmployees } from '@/lib/db/domains/employees'  // Modular  
import { supabase } from '@/lib/supabase'        // Direct
```

**Solution**: Consolidate to modular pattern

#### Step 1.1.1: Create Unified Database Interface

Create `lib/db/index.ts`:
```typescript
// Export all domain functions as single interface
export * from './domains/employees'
export * from './domains/appraisals'
export * from './domains/managers'
export * from './domains/feedback'
export * from './domains/pto'
export * from './domains/templates'
export * from './domains/workflows'
export * from './domains/periods'
export * from './domains/departments'

// Deprecated: Legacy db object (keep for backward compatibility)
import { db as legacyDb } from './legacy'
export { legacyDb as db }
```

#### Step 1.1.2: Update All Imports

Replace throughout codebase:
```typescript
// OLD
import { db } from '@/lib/db'
const employees = await db.getEmployees()

// NEW
import { getEmployees } from '@/lib/db'
const employees = await getEmployees()
```

#### Step 1.1.3: Deprecation Warnings

Add to legacy db object:
```typescript
export const db = {
  getEmployees: (...args) => {
    console.warn('DEPRECATED: Use import { getEmployees } from "@/lib/db" instead')
    return getEmployees(...args)
  },
  // ... other methods
}
```

### 1.2 Standardize Naming Conventions

**Goal**: Consistent snake_case in database, camelCase in TypeScript

#### Step 1.2.1: Create Type Mapping Utilities

Create `lib/db/core/mappers.ts`:
```typescript
// Convert database snake_case to TypeScript camelCase
export function mapEmployeeFromDb(dbEmployee: any) {
  return {
    id: dbEmployee.id,
    fullName: dbEmployee.full_name,
    firstName: dbEmployee.first_name,
    lastName: dbEmployee.last_name,
    linkedinUrl: dbEmployee.linkedin_url,
    twitterUrl: dbEmployee.twitter_url,
    telegramUrl: dbEmployee.telegram_url,
    departmentId: dbEmployee.department_id,
    managerId: dbEmployee.manager_id,
    createdAt: dbEmployee.created_at,
    updatedAt: dbEmployee.updated_at,
    // ... other fields
  }
}

export function mapEmployeeToDb(employee: any) {
  return {
    id: employee.id,
    full_name: employee.fullName,
    first_name: employee.firstName,
    last_name: employee.lastName,
    linkedin_url: employee.linkedinUrl,
    twitter_url: employee.twitterUrl,
    telegram_url: employee.telegramUrl,
    department_id: employee.departmentId,
    manager_id: employee.managerId,
    created_at: employee.createdAt,
    updated_at: employee.updatedAt,
    // ... other fields
  }
}
```

#### Step 1.2.2: Update Domain Functions

Update all domain functions to use mappers:
```typescript
// lib/db/domains/employees.ts
import { mapEmployeeFromDb, mapEmployeeToDb } from '../core/mappers'

export async function getEmployees() {
  const { data, error } = await supabaseAdmin
    .from('appy_employees')
    .select('*')
  
  if (error) throw new Error(error.message)
  
  return data.map(mapEmployeeFromDb)
}
```

### 1.3 Validation & Testing

#### Validation Script
```bash
# Create validation script
cat > scripts/validate-phase1.js << 'EOF'
const { db } = require('../lib/db')
const { getEmployees } = require('../lib/db')

async function validatePhase1() {
  console.log('🧪 Testing Phase 1 changes...')
  
  // Test both old and new patterns work
  const employeesOld = await db.getEmployees()
  const employeesNew = await getEmployees()
  
  console.log(`✅ Old pattern: ${employeesOld.length} employees`)
  console.log(`✅ New pattern: ${employeesNew.length} employees`)
  
  if (employeesOld.length === employeesNew.length) {
    console.log('✅ Phase 1 validation passed!')
  } else {
    console.log('❌ Phase 1 validation failed!')
    process.exit(1)
  }
}

validatePhase1()
EOF

node scripts/validate-phase1.js
```

---

## 📋 Phase 2: Data Model Cleanup
*Risk Level: **Medium** | Duration: 3-4 days*

### 2.1 Fix Manager Reference System

**Problem**: Dual manager references (direct field + relationship table)

**Solution**: Migrate to single relationship table approach

#### Step 2.1.1: Data Migration Script

```sql
-- Create migration script: migrations/001_fix_manager_references.sql

-- Step 1: Ensure all direct manager relationships exist in relationship table
INSERT INTO appy_employee_managers (employee_id, manager_id, is_primary, assigned_at)
SELECT 
  e.id as employee_id,
  e.manager_id,
  true as is_primary,
  e.created_at as assigned_at
FROM appy_employees e
WHERE e.manager_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM appy_employee_managers em 
    WHERE em.employee_id = e.id 
    AND em.manager_id = e.manager_id 
    AND em.is_primary = true
  );

-- Step 2: Validate data consistency
DO $$
DECLARE
  inconsistent_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO inconsistent_count
  FROM appy_employees e
  LEFT JOIN appy_employee_managers em ON (
    e.id = em.employee_id 
    AND e.manager_id = em.manager_id 
    AND em.is_primary = true
  )
  WHERE e.manager_id IS NOT NULL 
    AND em.employee_id IS NULL;
  
  IF inconsistent_count > 0 THEN
    RAISE EXCEPTION 'Found % inconsistent manager references', inconsistent_count;
  END IF;
  
  RAISE NOTICE 'Manager reference validation passed!';
END $$;
```

#### Step 2.1.2: Update Application Code

Create new manager relationship functions:
```typescript
// lib/db/domains/employee-managers.ts
export async function getPrimaryManager(employeeId: string) {
  const { data, error } = await supabaseAdmin
    .from('appy_employee_managers')
    .select(`
      manager_id,
      appy_managers!inner (
        user_id,
        full_name,
        email
      )
    `)
    .eq('employee_id', employeeId)
    .eq('is_primary', true)
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') return null // Not found
    throw new Error(error.message)
  }
  
  return {
    managerId: data.manager_id,
    fullName: data.appy_managers.full_name,
    email: data.appy_managers.email
  }
}

export async function assignPrimaryManager(employeeId: string, managerId: string) {
  // Remove existing primary manager
  await supabaseAdmin
    .from('appy_employee_managers')
    .update({ is_primary: false })
    .eq('employee_id', employeeId)
    .eq('is_primary', true)
  
  // Add new primary manager
  const { error } = await supabaseAdmin
    .from('appy_employee_managers')
    .upsert({
      employee_id: employeeId,
      manager_id: managerId,
      is_primary: true,
      assigned_at: new Date().toISOString()
    })
  
  if (error) throw new Error(error.message)
}
```

#### Step 2.1.3: Gradual Migration

Update employee functions to use relationship table:
```typescript
// lib/db/domains/employees.ts
export async function getEmployeeWithManager(employeeId: string) {
  const { data, error } = await supabaseAdmin
    .from('appy_employees')
    .select(`
      *,
      appy_employee_managers!inner (
        manager_id,
        is_primary,
        appy_managers!inner (
          user_id,
          full_name,
          email
        )
      )
    `)
    .eq('id', employeeId)
    .eq('appy_employee_managers.is_primary', true)
    .single()
  
  if (error) throw new Error(error.message)
  
  return {
    ...mapEmployeeFromDb(data),
    primaryManager: data.appy_employee_managers[0]?.appy_managers
  }
}
```

### 2.2 Separate Status Enums

**Problem**: Mixed workflow and payment statuses in same enum

**Solution**: Create separate enums

#### Step 2.2.1: Create New Enums

```sql
-- migrations/002_separate_status_enums.sql

-- Create separate enums
CREATE TYPE appy_workflow_status AS ENUM ('draft', 'submitted', 'pending', 'approved', 'rejected');
CREATE TYPE appy_payment_status AS ENUM ('ready-to-pay', 'contact-manager', 'paid', 'on-hold');

-- Add new columns with new types
ALTER TABLE appy_appraisals 
ADD COLUMN workflow_status appy_workflow_status DEFAULT 'draft',
ADD COLUMN payment_status_new appy_payment_status;

-- Migrate existing data
UPDATE appy_appraisals SET 
  workflow_status = CASE 
    WHEN status IN ('draft', 'submitted', 'pending', 'approved') THEN status::text::appy_workflow_status
    ELSE 'draft'
  END,
  payment_status_new = CASE 
    WHEN payment_status IN ('ready-to-pay', 'contact-manager') THEN payment_status::text::appy_payment_status
    ELSE NULL
  END;

-- Validate migration
DO $$
DECLARE
  unmigrated_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO unmigrated_count
  FROM appy_appraisals 
  WHERE workflow_status IS NULL;
  
  IF unmigrated_count > 0 THEN
    RAISE EXCEPTION 'Found % unmigrated workflow statuses', unmigrated_count;
  END IF;
  
  RAISE NOTICE 'Status enum migration validation passed!';
END $$;
```

#### Step 2.2.2: Update TypeScript Types

```typescript
// lib/supabase.ts - Update database types
export type WorkflowStatus = 'draft' | 'submitted' | 'pending' | 'approved' | 'rejected'
export type PaymentStatus = 'ready-to-pay' | 'contact-manager' | 'paid' | 'on-hold'

// Update appraisal interface
export interface Appraisal {
  id: string
  employeeId: string
  periodId: string
  managerId: string
  workflowStatus: WorkflowStatus
  paymentStatus: PaymentStatus | null
  // ... other fields
}
```

### 2.3 Validation & Testing

```bash
# Create Phase 2 validation script
cat > scripts/validate-phase2.js << 'EOF'
async function validatePhase2() {
  console.log('🧪 Testing Phase 2 changes...')
  
  // Test manager relationships
  const employees = await getEmployees()
  for (const employee of employees.slice(0, 5)) {
    const manager = await getPrimaryManager(employee.id)
    console.log(`Employee ${employee.fullName} -> Manager ${manager?.fullName || 'None'}`)
  }
  
  // Test status enums
  const appraisals = await getAppraisals()
  const statusCounts = appraisals.reduce((acc, a) => {
    acc[a.workflowStatus] = (acc[a.workflowStatus] || 0) + 1
    return acc
  }, {})
  
  console.log('Workflow status distribution:', statusCounts)
  console.log('✅ Phase 2 validation passed!')
}

validatePhase2()
EOF
```

---

## 📋 Phase 3: Business Logic Consolidation
*Risk Level: **Higher** | Duration: 4-5 days*

### 3.1 Unify Approval Pathways

**Problem**: Direct approval functions vs workflow system

**Solution**: Route all approvals through workflow system

#### Step 3.1.1: Create Workflow Wrapper

```typescript
// lib/db/domains/unified-approvals.ts
export async function approveAppraisal(
  appraisalId: string, 
  approverId: string, 
  comments?: string
) {
  // Check if workflow exists
  let workflow = await getWorkflowByAppraisalId(appraisalId)
  
  if (!workflow) {
    // Create simple single-step workflow for backward compatibility
    workflow = await createApprovalWorkflow({
      appraisalId,
      workflowType: 'simple',
      steps: [{
        level: 1,
        approverId,
        approverRole: 'manager',
        stepType: 'required'
      }]
    })
  }
  
  // Process through workflow system
  return processApprovalStep(workflow.currentStepId, 'approved', approverId, comments)
}

export async function rejectAppraisal(
  appraisalId: string, 
  approverId: string, 
  reason: string
) {
  let workflow = await getWorkflowByAppraisalId(appraisalId)
  
  if (!workflow) {
    workflow = await createApprovalWorkflow({
      appraisalId,
      workflowType: 'simple',
      steps: [{
        level: 1,
        approverId,
        approverRole: 'manager',
        stepType: 'required'
      }]
    })
  }
  
  return processApprovalStep(workflow.currentStepId, 'rejected', approverId, reason)
}
```

#### Step 3.1.2: Migrate Existing Approvals

```sql
-- migrations/003_migrate_to_workflows.sql

-- Create workflows for existing approved/rejected appraisals
INSERT INTO appy_approval_workflows (appraisal_id, workflow_type, current_level, total_levels, status, completed_at)
SELECT 
  id as appraisal_id,
  'simple' as workflow_type,
  1 as current_level,
  1 as total_levels,
  CASE 
    WHEN workflow_status = 'approved' THEN 'completed'
    WHEN workflow_status = 'rejected' THEN 'rejected'
    ELSE 'pending'
  END as status,
  CASE 
    WHEN workflow_status IN ('approved', 'rejected') THEN submitted_at
    ELSE NULL
  END as completed_at
FROM appy_appraisals
WHERE workflow_status IN ('approved', 'rejected')
  AND NOT EXISTS (
    SELECT 1 FROM appy_approval_workflows w 
    WHERE w.appraisal_id = appy_appraisals.id
  );

-- Create corresponding approval steps
INSERT INTO appy_approval_steps (workflow_id, step_level, approver_id, status, approved_at)
SELECT 
  w.id as workflow_id,
  1 as step_level,
  a.manager_id as approver_id,
  CASE 
    WHEN a.workflow_status = 'approved' THEN 'approved'
    WHEN a.workflow_status = 'rejected' THEN 'rejected'
    ELSE 'pending'
  END as status,
  CASE 
    WHEN a.workflow_status = 'approved' THEN a.submitted_at
    ELSE NULL
  END as approved_at
FROM appy_approval_workflows w
JOIN appy_appraisals a ON w.appraisal_id = a.id
WHERE w.workflow_type = 'simple';
```

### 3.2 Clarify Template vs Hardcoded Questions

**Problem**: Three different question systems

**Solution**: Unified template system with backward compatibility

#### Step 3.2.1: Create Unified Question Handler

```typescript
// lib/db/domains/appraisal-questions.ts
export interface AppraisalQuestion {
  id: string
  type: 'text' | 'rating' | 'select' | 'boolean'
  question: string
  required: boolean
  options?: string[]
  value?: any
}

export async function getAppraisalQuestions(
  appraisalId: string
): Promise<AppraisalQuestion[]> {
  const appraisal = await getAppraisalById(appraisalId)
  
  // If template is used, return template questions
  if (appraisal.templateId) {
    const template = await getTemplateById(appraisal.templateId)
    return template.questions.map(q => ({
      ...q,
      value: appraisal.templateResponses?.[q.id]
    }))
  }
  
  // Otherwise, return legacy + new structured questions
  return [
    // Legacy questions
    { id: 'question_1', type: 'text', question: 'Legacy Question 1', required: false, value: appraisal.question1 },
    { id: 'question_2', type: 'text', question: 'Legacy Question 2', required: false, value: appraisal.question2 },
    // ... other legacy questions
    
    // New structured questions
    { id: 'key_contributions', type: 'text', question: 'Key Contributions', required: true, value: appraisal.keyContributions },
    { id: 'impact_rating', type: 'rating', question: 'Impact Rating (1-5)', required: true, value: appraisal.impactRating },
    // ... other structured questions
  ]
}

export async function saveAppraisalResponses(
  appraisalId: string,
  responses: Record<string, any>
) {
  const appraisal = await getAppraisalById(appraisalId)
  
  if (appraisal.templateId) {
    // Save as template responses
    await supabaseAdmin
      .from('appy_appraisals')
      .update({ template_responses: responses })
      .eq('id', appraisalId)
  } else {
    // Map to individual fields
    const updateData = {
      question_1: responses.question_1,
      question_2: responses.question_2,
      key_contributions: responses.key_contributions,
      impact_rating: responses.impact_rating,
      // ... map all fields
    }
    
    await supabaseAdmin
      .from('appy_appraisals')
      .update(updateData)
      .eq('id', appraisalId)
  }
}
```

### 3.3 Fix Revision System

**Problem**: Unclear revision handling with unique constraints

**Solution**: Clear revision strategy using separate table

#### Step 3.3.1: Create Revision History Table

```sql
-- migrations/004_create_revision_history.sql

CREATE TABLE appy_appraisal_revisions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appraisal_id UUID NOT NULL REFERENCES appy_appraisals(id) ON DELETE CASCADE,
  revision_number INTEGER NOT NULL,
  revision_data JSONB NOT NULL, -- Complete appraisal data at time of revision
  created_by TEXT NOT NULL REFERENCES appy_managers(user_id),
  created_at TIMESTAMPTZ DEFAULT now(),
  
  UNIQUE(appraisal_id, revision_number)
);

-- Create index for performance
CREATE INDEX idx_appraisal_revisions_appraisal_id ON appy_appraisal_revisions(appraisal_id);
```

#### Step 3.3.2: Implement Revision Logic

```typescript
// lib/db/domains/revisions.ts
export async function createRevision(appraisalId: string, userId: string) {
  // Get current appraisal data
  const currentAppraisal = await getAppraisalById(appraisalId)
  
  // Get next revision number
  const { data: lastRevision } = await supabaseAdmin
    .from('appy_appraisal_revisions')
    .select('revision_number')
    .eq('appraisal_id', appraisalId)
    .order('revision_number', { ascending: false })
    .limit(1)
    .single()
  
  const nextRevisionNumber = (lastRevision?.revision_number || 0) + 1
  
  // Save current state as revision
  await supabaseAdmin
    .from('appy_appraisal_revisions')
    .insert({
      appraisal_id: appraisalId,
      revision_number: nextRevisionNumber,
      revision_data: currentAppraisal,
      created_by: userId
    })
  
  // Update appraisal with revision info
  await supabaseAdmin
    .from('appy_appraisals')
    .update({
      revision_number: nextRevisionNumber,
      is_revision: nextRevisionNumber > 1,
      last_edited_at: new Date().toISOString()
    })
    .eq('id', appraisalId)
  
  return nextRevisionNumber
}

export async function getRevisionHistory(appraisalId: string) {
  const { data, error } = await supabaseAdmin
    .from('appy_appraisal_revisions')
    .select(`
      *,
      appy_managers!inner (
        full_name
      )
    `)
    .eq('appraisal_id', appraisalId)
    .order('revision_number', { ascending: false })
  
  if (error) throw new Error(error.message)
  
  return data.map(revision => ({
    revisionNumber: revision.revision_number,
    data: revision.revision_data,
    createdBy: revision.appy_managers.full_name,
    createdAt: revision.created_at
  }))
}
```

---

## 📋 Phase 4: Advanced Optimizations
*Risk Level: **Low** | Duration: 2-3 days*

### 4.1 Add Missing Constraints

```sql
-- migrations/005_add_constraints.sql

-- Prevent overlapping appraisal periods
ALTER TABLE appy_appraisal_periods 
ADD CONSTRAINT no_overlapping_periods 
EXCLUDE USING gist (
  daterange(start_date, end_date, '[]') WITH &&
) WHERE (status = 'active');

-- Ensure PTO balance consistency
ALTER TABLE appy_employee_pto_balances
ADD CONSTRAINT valid_pto_balance 
CHECK (used_days >= 0 AND used_days <= total_days);

-- Ensure valid date ranges for PTO requests
ALTER TABLE appy_pto_requests
ADD CONSTRAINT valid_pto_dates 
CHECK (end_date >= start_date);

-- Ensure positive days requested
ALTER TABLE appy_pto_requests
ADD CONSTRAINT positive_days_requested 
CHECK (days_requested > 0);
```

### 4.2 Implement Cleanup Policies

```sql
-- migrations/006_cleanup_policies.sql

-- Create function to cleanup old audit logs
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM appy_audit_log 
  WHERE created_at < NOW() - INTERVAL '2 years';
  
  RAISE NOTICE 'Cleaned up audit logs older than 2 years';
END;
$$ LANGUAGE plpgsql;

-- Create function to cleanup old notification logs
CREATE OR REPLACE FUNCTION cleanup_old_notification_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM appy_notification_log 
  WHERE created_at < NOW() - INTERVAL '1 year'
    AND email_sent = true;
  
  RAISE NOTICE 'Cleaned up old notification logs';
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup (if using pg_cron extension)
-- SELECT cron.schedule('cleanup-audit-logs', '0 2 * * 0', 'SELECT cleanup_old_audit_logs();');
-- SELECT cron.schedule('cleanup-notification-logs', '0 3 * * 0', 'SELECT cleanup_old_notification_logs();');
```

### 4.3 Performance Optimizations

```sql
-- migrations/007_performance_indexes.sql

-- Composite indexes for common queries
CREATE INDEX idx_appraisals_period_status ON appy_appraisals(period_id, workflow_status);
CREATE INDEX idx_appraisals_manager_period ON appy_appraisals(manager_id, period_id);
CREATE INDEX idx_feedback_status_created ON appy_employee_feedback(status, created_at);
CREATE INDEX idx_pto_requests_employee_status ON appy_pto_requests(employee_id, status);

-- Partial indexes for active records
CREATE INDEX idx_active_employees ON appy_employees(id) WHERE active = true;
CREATE INDEX idx_active_managers ON appy_managers(user_id) WHERE active = true;

-- Text search indexes (if needed)
CREATE INDEX idx_employees_name_search ON appy_employees USING gin(to_tsvector('english', full_name));
CREATE INDEX idx_feedback_content_search ON appy_employee_feedback USING gin(to_tsvector('english', subject || ' ' || message));
```

---

## 🧪 Comprehensive Testing Strategy

### Automated Testing Suite

Create `scripts/test-all-phases.js`:
```javascript
const tests = [
  require('./validate-phase1'),
  require('./validate-phase2'), 
  require('./validate-phase3'),
  require('./validate-phase4')
]

async function runAllTests() {
  console.log('🧪 Running comprehensive test suite...')
  
  for (let i = 0; i < tests.length; i++) {
    console.log(`\n📋 Testing Phase ${i + 1}...`)
    try {
      await tests[i]()
      console.log(`✅ Phase ${i + 1} tests passed!`)
    } catch (error) {
      console.error(`❌ Phase ${i + 1} tests failed:`, error.message)
      process.exit(1)
    }
  }
  
  console.log('\n🎉 All tests passed! System cleanup successful!')
}

runAllTests()
```

### Data Integrity Validation

```sql
-- Create comprehensive validation script
-- scripts/validate-data-integrity.sql

-- Check manager reference consistency
SELECT 'Manager Reference Check' as test_name,
  CASE 
    WHEN COUNT(*) = 0 THEN 'PASS'
    ELSE 'FAIL: ' || COUNT(*) || ' inconsistent references'
  END as result
FROM appy_employees e
LEFT JOIN appy_employee_managers em ON (
  e.id = em.employee_id 
  AND em.is_primary = true
)
LEFT JOIN appy_managers m ON em.manager_id = m.user_id
WHERE e.active = true 
  AND em.employee_id IS NULL;

-- Check appraisal workflow consistency  
SELECT 'Workflow Consistency Check' as test_name,
  CASE 
    WHEN COUNT(*) = 0 THEN 'PASS'
    ELSE 'FAIL: ' || COUNT(*) || ' appraisals without workflows'
  END as result
FROM appy_appraisals a
LEFT JOIN appy_approval_workflows w ON a.id = w.appraisal_id
WHERE a.workflow_status IN ('approved', 'rejected')
  AND w.id IS NULL;

-- Check PTO balance consistency
SELECT 'PTO Balance Check' as test_name,
  CASE 
    WHEN COUNT(*) = 0 THEN 'PASS'
    ELSE 'FAIL: ' || COUNT(*) || ' invalid PTO balances'
  END as result
FROM appy_employee_pto_balances
WHERE used_days > total_days OR used_days < 0;
```

---

## 🔄 Rollback Procedures

### Phase-by-Phase Rollback

Each phase includes rollback scripts:

```bash
# Phase 1 Rollback (Code changes only)
git checkout HEAD~1 -- lib/db/

# Phase 2 Rollback (Database changes)
psql -f migrations/rollback/002_rollback_status_enums.sql

# Phase 3 Rollback (Business logic)
psql -f migrations/rollback/003_rollback_workflows.sql
psql -f migrations/rollback/004_rollback_revisions.sql

# Phase 4 Rollback (Optimizations)
psql -f migrations/rollback/005_rollback_constraints.sql
```

---

## 📅 Implementation Timeline

### Week 1
- **Days 1-2**: Phase 1 (Code Organization)
- **Days 3-5**: Phase 2 (Data Model Cleanup)

### Week 2  
- **Days 1-3**: Phase 3 (Business Logic Consolidation)
- **Days 4-5**: Phase 4 (Advanced Optimizations)

### Week 3
- **Days 1-2**: Comprehensive testing and validation
- **Day 3**: Production deployment and monitoring

---

## 🎯 Success Metrics

### Technical Metrics
- **Code Consistency**: Single database access pattern
- **Data Integrity**: Zero inconsistent references
- **Performance**: <100ms average query time
- **Test Coverage**: 100% validation coverage

### Business Metrics
- **Zero Downtime**: No service interruptions
- **Data Accuracy**: All existing data preserved
- **Feature Parity**: All existing functionality maintained
- **User Experience**: No workflow disruptions

This plan provides a systematic, low-risk approach to cleaning up all architectural issues while maintaining full backward compatibility and system stability.
