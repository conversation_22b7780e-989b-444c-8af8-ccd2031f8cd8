# Team Page Debug Guide

## Issue
The team page shows "No team members assigned" even though:
1. CJN Automation has hr-admin role (should see all employees)
2. There are 160 employees in the database
3. CJN Automation has at least 1 employee assigned (Alpay Aktug)

## Debugging Steps

### 1. Test the API Endpoint
Visit: `http://localhost:3000/api/test-employees`

This will show:
- Current user details
- Count of all employees from getEmployees()
- Count of manager's employees from getEmployeesForManager()

### 2. Check Browser Console
When on the team page, check the browser console for these logs:
```
[TEAM PAGE] Loading team page
[TEAM PAGE] User found: CJN Automation Role: hr-admin
[TEAM PAGE] Loading team content for user: CJN Automation Role: hr-admin
[TEAM PAGE] Admin view - Loaded X employees
```

### 3. Check Server Logs
Look for these server-side logs:
```
📋 [DEBUG] getEmployees - Starting to fetch all employees
📋 [DEBUG] getEmployees - Fetched X employees from database
📋 [DEBUG] getEmployees - Returning X mapped employees
```

## Potential Issues

### 1. Database Connection
The db.getEmployees() might be failing silently. Check for:
- Database connection errors
- Permission issues with the service role key

### 2. Data Mapping
The employee data might not be mapping correctly due to:
- Missing department relationships
- Null values in required fields

### 3. Client-Side Rendering
The team page might be:
- Not waiting for data to load
- Having hydration issues
- Showing cached empty state

## Quick Fix Attempts

### 1. Clear Browser Cache
- Hard refresh: Cmd+Shift+R (Mac) or Ctrl+Shift+R (Windows)
- Clear site data in Developer Tools

### 2. Check Environment Variables
Ensure these are set correctly:
```
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
```

### 3. Test Direct Database Query
Run this in Supabase SQL editor:
```sql
SELECT COUNT(*) FROM appy_employees WHERE active = true;
```

### 4. Verify User Role
Check CJN Automation's role:
```sql
SELECT * FROM appy_managers WHERE email = '<EMAIL>';
```

## Next Steps

1. **Run the test endpoint** and share the results
2. **Check browser console** for any errors
3. **Check server logs** for database errors
4. **Try a different browser** to rule out caching issues

The test endpoint will help identify if:
- The user authentication is working
- The database queries are returning data
- The data transformation is working correctly