# Manager Assignment Fix

## Issue
The manager assignment was failing with error:
```
Could not choose the best candidate function between: 
public.assign_primary_manager(p_employee_id => uuid, p_manager_id => text, p_assigned_at => timestamp with time zone), 
public.assign_primary_manager(p_employee_id => text, p_manager_id => text, p_assigned_at => timestamp with time zone)
```

## Root Cause
There were two conflicting versions of the `assign_primary_manager` function in the database:
1. One expecting `employee_id` as UUID (correct)
2. One expecting `employee_id` as TEXT (incorrect)

## Solution Applied

### 1. Database Changes
- Dropped the incorrect TEXT version of the function
- Kept only the UUID version that matches the actual column type
- Added a unique constraint to ensure only one primary manager per employee

### 2. Code Changes
- Added UUID format validation in `lib/db/domains/relationships.ts`
- Added explicit type casting in the RPC call to ensure proper type handling

### 3. Database Constraint
Created a partial unique index:
```sql
CREATE UNIQUE INDEX idx_unique_primary_manager 
ON appy_employee_managers (employee_id) 
WHERE is_primary = true;
```

## Multi-Level Manager Hierarchy Assessment
The current database design is adequate for multi-level hierarchies:
- `appy_managers` table has `manager_id` field for hierarchical relationships
- `appy_employee_managers` junction table supports many-to-many relationships
- `is_primary` flag allows designation of primary manager
- System can handle multiple managers per employee with proper hierarchy

## Testing
The fix has been implemented and the function signature now correctly expects:
- `p_employee_id` as UUID
- `p_manager_id` as TEXT (Clerk user ID)
- `p_assigned_at` as timestamp with time zone