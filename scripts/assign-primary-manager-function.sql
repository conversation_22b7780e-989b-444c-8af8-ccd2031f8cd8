-- PostgreSQL function to atomically assign a primary manager to an employee
-- This ensures that both the update and insert operations are executed within a single transaction
-- to maintain data consistency and avoid race conditions.

CREATE OR REPLACE FUNCTION assign_primary_manager(
  p_employee_id TEXT,
  p_manager_id TEXT,
  p_assigned_at TIMESTAMPTZ DEFAULT NOW()
)
RETURNS TABLE(
  id UUID,
  employee_id TEXT,
  manager_id TEXT,
  is_primary BOOLEAN,
  assigned_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
) AS $$
DECLARE
  v_result RECORD;
BEGIN
  -- Log the operation for debugging
  RAISE NOTICE 'Assigning primary manager: employee_id=%, manager_id=%', p_employee_id, p_manager_id;
  
  -- Start transaction (implicit in function)
  -- First, remove primary flag from all existing managers for this employee
  UPDATE appy_employee_managers 
  SET is_primary = false
  WHERE employee_id = p_employee_id 
    AND is_primary = true;
  
  -- Log how many records were updated
  RAISE NOTICE 'Updated % existing primary manager records to non-primary', ROW_COUNT;
  
  -- Then, insert or update the new primary manager
  INSERT INTO appy_employee_managers (
    employee_id,
    manager_id,
    is_primary,
    assigned_at
  ) VALUES (
    p_employee_id,
    p_manager_id,
    true,
    p_assigned_at
  )
  ON CONFLICT (employee_id, manager_id) 
  DO UPDATE SET 
    is_primary = true,
    assigned_at = p_assigned_at
  RETURNING 
    appy_employee_managers.id,
    appy_employee_managers.employee_id,
    appy_employee_managers.manager_id,
    appy_employee_managers.is_primary,
    appy_employee_managers.assigned_at,
    appy_employee_managers.created_at
  INTO v_result;
  
  -- Log the successful operation
  RAISE NOTICE 'Successfully assigned primary manager with ID: %', v_result.id;
  
  -- Return the result
  RETURN QUERY SELECT 
    v_result.id,
    v_result.employee_id,
    v_result.manager_id,
    v_result.is_primary,
    v_result.assigned_at,
    v_result.created_at;
    
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error and re-raise it
    RAISE EXCEPTION 'Failed to assign primary manager: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
-- Adjust this based on your RLS policies and user roles
GRANT EXECUTE ON FUNCTION assign_primary_manager(TEXT, TEXT, TIMESTAMPTZ) TO authenticated;

-- Add a comment explaining the function
COMMENT ON FUNCTION assign_primary_manager(TEXT, TEXT, TIMESTAMPTZ) IS 
'Atomically assigns a primary manager to an employee. Removes primary flag from existing managers and sets the new manager as primary in a single transaction.';
