"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { MoreHorizontal, ArrowUpDown, User, Edit, UserX, UserCheck, Trash2 } from "lucide-react"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import type { Em<PERSON><PERSON><PERSON>, Department, Manager, UserSession } from "@/lib/types"
import { EmployeeFormDialog } from "./employee-form-dialog"
import { removeEmployeeAction, deleteEmployeeAction } from "@/lib/actions/employees"
import { toast } from "sonner"
import {
  announceToScreenReader,
  getStatusAnnouncement,
  getTableAnnouncement,
  getSortAnnouncement
} from "@/lib/accessibility"

export function EmployeesTable({
  data,
  departments,
  managers,
  onRefresh,
  user
}: {
  data: Employee[]
  departments: Department[]
  managers: Manager[]
  onRefresh?: () => void
  user?: UserSession | null
}) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [isFormOpen, setIsFormOpen] = React.useState(false)
  const [selectedEmployee, setSelectedEmployee] = React.useState<Employee | null>(null)
  const [announceUpdate, setAnnounceUpdate] = React.useState<string>("")
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = React.useState(false)
  const [employeeToRemove, setEmployeeToRemove] = React.useState<Employee | null>(null)
  const [confirmRemoval, setConfirmRemoval] = React.useState(false)
  const [isRemoving, setIsRemoving] = React.useState(false)

  // Hide compensation rates from all users
  const canViewRates = false

  // Announce table updates to screen readers
  React.useEffect(() => {
    if (announceUpdate) {
      announceToScreenReader(announceUpdate)
      setAnnounceUpdate("")
    }
  }, [announceUpdate])

  // Announce table information on mount
  React.useEffect(() => {
    const tableAnnouncement = getTableAnnouncement(data.length, 5)
    announceToScreenReader(tableAnnouncement)
  }, [data.length])

  // Note: Edit functionality now redirects to profile edit page
  // This function is kept for potential future use but not currently called

  const handleAddNew = () => {
    console.log('[A11Y] Opening new employee dialog')
    setSelectedEmployee(null)
    setIsFormOpen(true)
    setAnnounceUpdate("Opening new employee form")
  }

  const handleFormClose = () => {
    console.log('[A11Y] Closing employee form dialog')
    setIsFormOpen(false)
    setAnnounceUpdate("Employee form closed")
    setTimeout(() => setSelectedEmployee(null), 300)
  }

  const handleDeactivate = async (employee: Employee) => {
    try {
      const result = await deleteEmployeeAction(employee.id)
      if (result.success) {
        toast.success('message' in result ? result.message : 'Employee deactivated successfully')
        onRefresh?.()
      } else {
        toast.error('error' in result ? result.error : 'Failed to deactivate employee')
      }
    } catch (error) {
      toast.error('Failed to deactivate employee')
    }
  }

  const handleRemoveClick = (employee: Employee) => {
    setEmployeeToRemove(employee)
    setIsRemoveDialogOpen(true)
    setConfirmRemoval(false)
  }

  const handleRemoveConfirm = async () => {
    if (!employeeToRemove || !confirmRemoval) return

    setIsRemoving(true)
    try {
      const result = await removeEmployeeAction(employeeToRemove.id)
      if (result.success) {
        toast.success('message' in result ? result.message : 'Employee removed successfully')
        setIsRemoveDialogOpen(false)
        setEmployeeToRemove(null)
        setConfirmRemoval(false)
        onRefresh?.()
      } else {
        toast.error('error' in result ? result.error : 'Failed to remove employee')
      }
    } catch (error) {
      toast.error('Failed to remove employee')
    } finally {
      setIsRemoving(false)
    }
  }

  const handleRemoveCancel = () => {
    setIsRemoveDialogOpen(false)
    setEmployeeToRemove(null)
    setConfirmRemoval(false)
  }

  // Build columns array dynamically based on permissions
  const columns: ColumnDef<Employee>[] = React.useMemo(() => {
    const baseColumns: ColumnDef<Employee>[] = [
      {
        accessorKey: "fullName",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => {
              const newSorting = column.getIsSorted() === "asc" ? "desc" : "asc"
              column.toggleSorting(column.getIsSorted() === "asc")
              setAnnounceUpdate(getSortAnnouncement("Name", newSorting))
            }}
            className="h-auto p-0 font-medium hover:bg-transparent"
            aria-label={getSortAnnouncement("Name", column.getIsSorted() || null)}
          >
            Name
            <ArrowUpDown className="ml-2 h-4 w-4" aria-hidden="true" />
          </Button>
        ),
      },
      {
        accessorKey: "departmentName",
        header: ({ column }) => (
          <Button
            variant="ghost"
            onClick={() => {
              const newSorting = column.getIsSorted() === "asc" ? "desc" : "asc"
              column.toggleSorting(column.getIsSorted() === "asc")
              setAnnounceUpdate(getSortAnnouncement("Department", newSorting))
            }}
            className="h-auto p-0 font-medium hover:bg-transparent"
            aria-label={getSortAnnouncement("Department", column.getIsSorted() || null)}
          >
            Department
            <ArrowUpDown className="ml-2 h-4 w-4" aria-hidden="true" />
          </Button>
        ),
      },
      {
        accessorKey: "managerName",
        header: "Managers",
        cell: ({ row }) => {
          const employee = row.original

          // Handle multiple managers
          if (employee.managers && employee.managers.length > 0) {
            const primaryManager = employee.managers.find(m => m.isPrimary)
            const otherManagers = employee.managers.filter(m => !m.isPrimary)

            return (
              <div className="space-y-1">
                {primaryManager && (
                  <div className="flex items-center gap-1">
                    <Badge variant="secondary" className="text-xs">Primary</Badge>
                    <span className="text-sm">{primaryManager.managerName}</span>
                  </div>
                )}
                {otherManagers.map((manager, index) => (
                  <div key={manager.id} className="text-sm text-muted-foreground">
                    {manager.managerName}
                  </div>
                ))}
                {employee.managers.length > 1 && (
                  <div className="text-xs text-muted-foreground">
                    {employee.managers.length} total
                  </div>
                )}
              </div>
            )
          }

          // Handle legacy single manager
          if (employee.managerName) {
            return <span className="text-sm">{employee.managerName}</span>
          }

          return <span className="text-sm text-muted-foreground">No manager</span>
        },
      },
    ]

    // Conditionally add compensation column
    if (canViewRates) {
      baseColumns.push({
        accessorKey: "compensation",
        header: "Compensation",
        cell: ({ row }) => <div className="capitalize">{row.getValue("compensation") as string}</div>,
      })
    }

    // Add status column (always shown)
    baseColumns.push({
      accessorKey: "active",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("active")
        const statusText = isActive ? "Active" : "Inactive"
        return (
          <Badge
            variant={isActive ? "default" : "outline"}
            className={isActive ? "bg-green-600" : ""}
            aria-label={getStatusAnnouncement(statusText.toLowerCase())}
          >
            {statusText}
          </Badge>
        )
      },
    })

    // Add actions column (always shown)
    baseColumns.push({
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => {
        const employee = row.original
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                aria-label={`Actions for ${employee.fullName}`}
              >
                <span className="sr-only">Open menu for {employee.fullName}</span>
                <MoreHorizontal className="h-4 w-4" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions for {employee.fullName}</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${employee.id}/profile`} className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${employee.id}/profile/edit`} className="flex items-center">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Employee
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDeactivate(employee)}>
                {employee.active ? (
                  <>
                    <UserX className="mr-2 h-4 w-4" />
                    Deactivate Employee
                  </>
                ) : (
                  <>
                    <UserCheck className="mr-2 h-4 w-4" />
                    Activate Employee
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleRemoveClick(employee)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Remove Employee
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )
      },
    })

    return baseColumns
  }, [canViewRates, setAnnounceUpdate])

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters },
  })

  return (
    <div>
      <div className="flex items-center py-4">
        <Input
          placeholder="Filter by name..."
          value={(table.getColumn("fullName")?.getFilterValue() as string) ?? ""}
          onChange={(event) => {
            const value = event.target.value
            table.getColumn("fullName")?.setFilterValue(value)
            if (value) {
              setAnnounceUpdate(`Filtering employees by name: ${value}`)
            } else {
              setAnnounceUpdate("Filter cleared, showing all employees")
            }
          }}
          className="max-w-sm"
          aria-label="Filter employees by name"
        />
        {(user?.role === 'super-admin' || user?.role === 'hr-admin') && (
          <Button onClick={handleAddNew} className="ml-auto">
            Add New Employee
          </Button>
        )}
      </div>
      <div className="rounded-md border">
        <Table
          aria-label={getTableAnnouncement(data.length, 5)}
          role="table"
        >
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} role="row">
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} role="columnheader">
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  role="row"
                  aria-rowindex={index + 2} // +2 because header is row 1
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      role="gridcell"
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow role="row">
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                  role="gridcell"
                  aria-label="No employees found"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <nav className="flex items-center justify-end space-x-2 py-4" aria-label="Table pagination">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            table.previousPage()
            setAnnounceUpdate("Moved to previous page")
          }}
          disabled={!table.getCanPreviousPage()}
          aria-label="Go to previous page"
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            table.nextPage()
            setAnnounceUpdate("Moved to next page")
          }}
          disabled={!table.getCanNextPage()}
          aria-label="Go to next page"
        >
          Next
        </Button>
      </nav>
      <EmployeeFormDialog
        isOpen={isFormOpen}
        onClose={handleFormClose}
        employee={selectedEmployee}
        departments={departments}
        managers={managers}
        user={user}
      />

      {/* Remove Employee Confirmation Dialog */}
      <Dialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Remove Employee</DialogTitle>
            <DialogDescription>
              Are you sure you want to permanently remove <strong>{employeeToRemove?.fullName}</strong> from the system?
              <br /><br />
              <span className="text-red-600 font-medium">
                ⚠️ This action cannot be undone. All employee data, appraisals, and related records will be permanently deleted.
              </span>
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center space-x-2 py-4">
            <Checkbox
              id="confirm-removal"
              checked={confirmRemoval}
              onCheckedChange={(checked) => setConfirmRemoval(checked === true)}
            />
            <label
              htmlFor="confirm-removal"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Yes, I understand this action cannot be undone
            </label>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleRemoveCancel}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemoveConfirm}
              disabled={!confirmRemoval || isRemoving}
            >
              {isRemoving ? "Removing..." : "Remove Employee"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
