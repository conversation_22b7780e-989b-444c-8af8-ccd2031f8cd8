"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface DebugData {
  user?: any
  teamMembers?: any[]
  error?: string
}

export function DashboardDebug() {
  const [debugData, setDebugData] = useState<DebugData>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetch('/api/test-employees')
      .then(res => res.json())
      .then(data => {
        setDebugData(data)
        setLoading(false)
        console.log('🧪 [CLIENT DEBUG] Dashboard data:', data)
      })
      .catch(error => {
        setDebugData({ error: error.message })
        setLoading(false)
        console.error('🧪 [CLIENT DEBUG] Error:', error)
      })
  }, [])

  if (process.env.NODE_ENV === 'production') {
    return null // Don't show in production
  }

  return (
    <Card className="border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle className="text-sm text-yellow-800">🧪 Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="text-xs space-y-2">
        {loading ? (
          <p>Loading debug data...</p>
        ) : (
          <>
            <div>
              <strong>User:</strong> {debugData.user?.fullName} ({debugData.user?.role})
            </div>
            <div>
              <strong>All Employees:</strong> {debugData.allEmployees?.count || 0}
            </div>
            <div>
              <strong>Manager Employees:</strong> {debugData.managerEmployees?.count || 0}
            </div>
            {debugData.error && (
              <div className="text-red-600">
                <strong>Error:</strong> {debugData.error}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}