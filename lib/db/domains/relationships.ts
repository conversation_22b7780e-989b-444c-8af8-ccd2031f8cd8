import { supabaseAdmin } from '../../supabase'
import type { EmployeeManager } from '../core/types'

// Employee-Manager relationship functions
export async function getEmployeeManagers(employeeId: string): Promise<EmployeeManager[]> {
  const { data, error } = await supabaseAdmin.from('appy_employee_managers')
    .select(`
      *,
      appy_managers:manager_id (
        full_name
      )
    `)
    .eq('employee_id', employeeId)
    .order('is_primary', { ascending: false })
  
  if (error) {
    throw new Error(error.message)
  }
  
  return (data || []).map(em => ({
    id: em.id,
    employeeId: em.employee_id,
    managerId: em.manager_id,
    managerName: em.appy_managers?.full_name || '',
    isPrimary: em.is_primary,
    assignedAt: em.assigned_at || em.created_at
  }))
}

export async function assignManagerToEmployee(employeeId: string, managerId: string, isPrimary = false): Promise<EmployeeManager> {
  console.log('🔄 [DB] Assigning manager to employee:', { employeeId, managerId, isPrimary })

  // Validate UUID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(employeeId)) {
    console.error('❌ [DB] Invalid employee ID format:', employeeId)
    throw new Error('Invalid employee ID format - must be a valid UUID')
  }

  // Use a transaction to ensure atomicity when setting primary manager
  if (isPrimary) {
    console.log('🔄 [DB] Setting as primary manager - using transaction for atomic operations')

    // Execute both operations in a transaction using Supabase RPC
    // Ensure employeeId is properly typed as UUID
    const { data: assignmentId, error } = await supabaseAdmin.rpc('assign_primary_manager', {
      p_employee_id: employeeId as any, // Supabase will handle UUID conversion
      p_manager_id: managerId,
      p_assigned_at: new Date().toISOString()
    })

    if (error) {
      console.error('❌ [DB] Failed to assign primary manager:', error)
      throw new Error(error.message)
    }

    console.log('✅ [DB] Successfully assigned primary manager with ID:', assignmentId)

    // Fetch the created record to return the expected format
    const { data: createdRecord, error: fetchError } = await supabaseAdmin
      .from('appy_employee_managers')
      .select('*')
      .eq('id', assignmentId)
      .single()

    if (fetchError) {
      console.error('❌ [DB] Failed to fetch created manager assignment:', fetchError)
      throw new Error(fetchError.message)
    }

    return {
      id: createdRecord.id,
      employeeId: createdRecord.employee_id,
      managerId: createdRecord.manager_id,
      managerName: '',
      isPrimary: createdRecord.is_primary,
      assignedAt: createdRecord.assigned_at || createdRecord.created_at
    }
  } else {
    // For non-primary managers, simple insert is sufficient
    console.log('🔄 [DB] Adding non-primary manager')

    const { data, error } = await supabaseAdmin.from('appy_employee_managers')
      .insert({
        employee_id: employeeId,
        manager_id: managerId,
        is_primary: false,
        assigned_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('❌ [DB] Failed to assign non-primary manager:', error)
      throw new Error(error.message)
    }

    console.log('✅ [DB] Successfully assigned non-primary manager')
    return {
      id: data.id,
      employeeId: data.employee_id,
      managerId: data.manager_id,
      managerName: '',
      isPrimary: data.is_primary,
      assignedAt: data.assigned_at || data.created_at
    }
  }
}

export async function removeManagerFromEmployee(employeeId: string, managerId: string): Promise<void> {
  const { error } = await supabaseAdmin.from('appy_employee_managers')
    .delete()
    .eq('employee_id', employeeId)
    .eq('manager_id', managerId)
  
  if (error) {
    throw new Error(error.message)
  }
}

export async function getAppraisalCompletionStatus(employeeId: string, periodId: string): Promise<{
  totalManagers: number
  completedManagers: number
  managersStatus: Array<{
    managerId: string
    managerName: string
    status: string
    submittedAt?: string
    isPrimary: boolean
  }>
  allManagersCompleted: boolean
}> {
  // Get all managers for this employee
  const managers = await getEmployeeManagers(employeeId)
  
  if (managers.length === 0) {
    return {
      totalManagers: 0,
      completedManagers: 0,
      managersStatus: [],
      allManagersCompleted: false
    }
  }

  // Get appraisal status for each manager
  const managersStatus = await Promise.all(
    managers.map(async (manager) => {
      const { data: appraisal } = await supabaseAdmin.from('appy_appraisals')
        .select('status, submitted_at')
        .eq('employee_id', employeeId)
        .eq('period_id', periodId)
        .eq('manager_id', manager.managerId)
        .single()

      return {
        managerId: manager.managerId,
        managerName: manager.managerName || 'Unknown Manager',
        status: appraisal?.status || 'not-started',
        submittedAt: appraisal?.submitted_at,
        isPrimary: manager.isPrimary
      }
    })
  )

  const completedManagers = managersStatus.filter(
    m => m.status === 'submitted' || m.status === 'approved'
  ).length

  return {
    totalManagers: managers.length,
    completedManagers,
    managersStatus,
    allManagersCompleted: completedManagers === managers.length
  }
}